@* @page "~/ExamManagement" *@
@model EdTech.Study.Web.Pages.Exams.IndexModel
@using Microsoft.Extensions.Localization
@using EdTech.Study.Localization
@using Newtonsoft.Json
@using Newtonsoft.Json.Serialization
@inject IStringLocalizer<EdTech.Study.Localization.StudyResource> L
@{
    ViewData["Title"] = L["Title:ExamManagement"];
}

@section Styles {
    <link rel="stylesheet" href="/EdTech/reactapp/assets/css/app.css" asp-append-version="true">
    <link rel="stylesheet" href="/EdTech/reactapp/assets/css/vendor.css" asp-append-version="true">
}


<div id="ExamManagementPage"></div>

@section Scripts {
    @* <script src="/js/store/lesson-config-db-module.js" asp-append-version="true"></script> *@
    @* <script src="/js/common/lesson-init-module.js" asp-append-version="true"></script> *@
    <script src="/EdTech/reactapp/runtime.js" asp-append-version="true"></script>
    <script type="module" src="/EdTech/reactapp/ExamManagementPage.js" asp-append-version="true"></script>
}