// Header JavaScript

// Add smooth scrolling for navigation links
document.querySelectorAll('.nav-item').forEach((link) => {
    link.addEventListener('click', (e) => {
        e.preventDefault();

        // Remove active class from all links
        document.querySelectorAll('.nav-item').forEach((item) => {
            item.classList.remove('active');
        });

        // Add active class to clicked link
        link.classList.add('active');
    });
});

// Header scroll behavior (optional - add sticky header effects)
window.addEventListener('scroll', () => {
    const header = document.querySelector('.header');
    if (window.scrollY > 100) {
        header.classList.add('scrolled');
    } else {
        header.classList.remove('scrolled');
    }
});

// Mobile menu toggle (if needed for responsive design)
function toggleMobileMenu() {
    const navMenu = document.querySelector('.nav-menu');
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    
    if (navMenu && mobileMenuBtn) {
        mobileMenuBtn.addEventListener('click', () => {
            navMenu.classList.toggle('active');
            mobileMenuBtn.classList.toggle('active');
        });
    }
}

// Auth section handling
function handleAuthActions() {
    const authLinks = document.querySelectorAll('.auth-link');
    
    authLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const action = link.textContent.trim().toLowerCase();
            
            if (action.includes('đăng nhập') || action.includes('login')) {
                // Handle login
                console.log('Login clicked');
                // Add your login logic here
            } else if (action.includes('đăng ký') || action.includes('register')) {
                // Handle register
                console.log('Register clicked');
                // Add your register logic here
            }
        });
    });
}

// Footer JavaScript

// Handle social widget clicks
document.querySelectorAll('.social-widget img').forEach((icon, index) => {
    icon.addEventListener('click', () => {
        // Add your social media link logic here
        const socialPlatforms = ['facebook', 'twitter', 'instagram', 'youtube'];
        const platform = socialPlatforms[index] || 'unknown';
        console.log('Social icon clicked:', platform);
        
        // You can add actual social media URLs here
        const socialUrls = {
            facebook: 'https://facebook.com/your-page',
            twitter: 'https://twitter.com/your-account',
            instagram: 'https://instagram.com/your-account',
            youtube: 'https://youtube.com/your-channel'
        };
        
        if (socialUrls[platform]) {
            window.open(socialUrls[platform], '_blank');
        }
    });
});

// Handle footer social icons clicks
document.querySelectorAll('.social-icon').forEach((icon, index) => {
    icon.addEventListener('click', () => {
        const socialPlatforms = ['facebook', 'twitter', 'instagram', 'linkedin'];
        const platform = socialPlatforms[index] || 'unknown';
        console.log('Footer social icon clicked:', platform);
        
        // Add actual social media URLs here
        const socialUrls = {
            facebook: 'https://facebook.com/your-page',
            twitter: 'https://twitter.com/your-account',
            instagram: 'https://instagram.com/your-account',
            linkedin: 'https://linkedin.com/company/your-company'
        };
        
        if (socialUrls[platform]) {
            window.open(socialUrls[platform], '_blank');
        }
    });
});

// Handle footer links
document.querySelectorAll('.footer-links a').forEach(link => {
    link.addEventListener('click', (e) => {
        e.preventDefault();
        const linkText = link.textContent.trim();
        console.log('Footer link clicked:', linkText);
        
        // Add your footer link navigation logic here
        // For example, you might want to scroll to sections or navigate to pages
    });
});

// Newsletter subscription (if you have a newsletter form in footer)
function handleNewsletterSubscription() {
    const newsletterForm = document.querySelector('.newsletter-form');
    const emailInput = document.querySelector('.newsletter-email');
    const subscribeBtn = document.querySelector('.newsletter-btn');
    
    if (newsletterForm && emailInput && subscribeBtn) {
        newsletterForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const email = emailInput.value.trim();
            
            if (email && isValidEmail(email)) {
                console.log('Newsletter subscription:', email);
                // Add your newsletter subscription logic here
                
                // Show success message
                showNotification('Đăng ký thành công!', 'success');
                emailInput.value = '';
            } else {
                showNotification('Vui lòng nhập email hợp lệ!', 'error');
            }
        });
    }
}

// Email validation helper
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Notification helper
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Back to top button (usually in footer area)
function handleBackToTop() {
    const backToTopBtn = document.querySelector('.back-to-top');
    
    if (backToTopBtn) {
        // Show/hide button based on scroll position
        window.addEventListener('scroll', () => {
            if (window.scrollY > 300) {
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.remove('visible');
            }
        });
        
        // Handle click
        backToTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
}

// Initialize layout JavaScript when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize header functions
    toggleMobileMenu();
    handleAuthActions();
    
    // Initialize footer functions
    handleNewsletterSubscription();
    handleBackToTop();
    
    console.log('Layout JavaScript initialized');
});
